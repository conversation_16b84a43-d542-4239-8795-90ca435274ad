{"name": "otechq", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "pnpm -r run start", "clean": "find ./ -name 'node_modules' -type d -exec rm -rf {} +", "clean:web": "find ./ -name '.next' -type d -exec rm -rf {} +", "update:all": "pnpm -r update -i --latest", "web:dev": "pnpm --filter @otechq/web run dev", "web:build": "pnpm --filter @otechq/web run build", "web:preview": "pnpm --filter @otechq/web run preview", "pre-commit": "pnpm --filter @otechq/* exec lint-staged", "prepare": "test -d .git && husky || echo 'Skipping husky prepare: no .git directory'"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">= 18.0.0", "pnpm": ">= 9.15.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "husky": "^9.1.7"}, "packageManager": "pnpm@10.3.0"}