# Node.js specific files and directories
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-store/ # pnpm's global content-addressable store (if used outside the workspace)
.pnpm-cache/ # pnpm's local cache
.env # Local environment variables (sensitive)
.env.*.local # More specific local environment variables

# Common build output directories
dist/
build/
out/ # Next.js default output directory if not using 'standalone'

# Git and editor related files
.git
.gitignore
.editorconfig
.prettierrc*
.eslintrc*
.eslintcache
.vscode/
*.swp
*.pem
.DS_Store
.idea/

# Development tools
.husky/
.keys/
.md

# Local environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.example
.env.*

# Typescript
*.tsbuildinfo
next-env.d.ts

# Vercel
.vercel/

# Docker
Dockerfile
docker-compose.yml
.dockerignore
.env.docker.example

# IDE and OS generated files
.idea/
Thumbs.db

# Logs
logs/
*.log

# Specific application build outputs that will be generated inside the container
# or are not needed in the final image.
# The Dockerfiles build these inside the container, so we don't need to send
# them from the host build context.
apps/web/.next/
apps/web/node_modules/ # Copied from deps stage, not from host

# Any local temporary files or directories
tmp/
temp/
*.tmp

# Testing
coverage/
*.lcov