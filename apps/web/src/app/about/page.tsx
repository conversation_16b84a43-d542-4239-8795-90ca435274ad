import type { Metadata } from "next";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@web/_lib/components/features/mdx-render";
import getMDXMetadata from "@web/_lib/utils/get-mdx-metadata";

// Metadata
export async function generateMetadata(): Promise<Metadata> {
  const metadata = await getMDXMetadata("curriculum-vitae.mdx");
  return metadata;
}

export default function About(): React.ReactElement {
  return <MDXRenderer source="curriculum-vitae.mdx" />;
}
