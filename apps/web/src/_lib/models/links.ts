import { v4 as uuidv4 } from "uuid";
export type MediaSocialLinksType = {
  id: string;
  name: string;
  url: string;
  lightIcon: string;
  darkIcon: string;
};
export type NavigationLinksType = {
  id: string;
  name: string;
  url: string;
  icon: string;
  show: boolean;
};

// @data
export const MediaSocialLinks: MediaSocialLinksType[] = [
  {
    id: uuidv4(),
    name: "LinkedIn",
    url: "https://www.linkedin.com/in/a-lee0510/",
    lightIcon: "/icons/linkedin-light.svg",
    darkIcon: "/icons/linkedin-dark.svg",
  },
  {
    id: uuidv4(),
    name: "GitHub",
    url: "https://github.com/alee0510",
    lightIcon: "/icons/github-light.svg",
    darkIcon: "/icons/github-dark.svg",
  },
  {
    id: uuidv4(),
    name: "Twitter",
    url: "https://x.com/a_lee0510",
    lightIcon: "/icons/twitter-light.svg",
    darkIcon: "/icons/twitter-dark.svg",
  },
];

export const NavigationLinks: NavigationLinksType[] = [
  {
    id: uuidv4(),
    name: "Home",
    url: "/",
    icon: "Home",
    show: true,
  },
  {
    id: uuidv4(),
    name: "Articles",
    url: "/articles",
    icon: "BookOpen",
    show: true,
  },
  {
    id: uuidv4(),
    name: "Projects",
    url: "/projects",
    icon: "BriefcaseBusiness",
    show: false,
  },
  {
    id: uuidv4(),
    name: "Courses",
    url: "/courses",
    icon: "MonitoPlay",
    show: false,
  },
  {
    id: uuidv4(),
    name: "About Me",
    url: "/about",
    icon: "UserRound",
    show: true,
  },
];
