---
title: About <PERSON> | Software Engineer
description: Creative and passionate Software Engineer with over 4 years of experience in diverse tech stacks and programming languages.
keywords:
  [
    Software Engineer,
    Full-stack Developer,
    Web Developer,
    Mobile App Developer,
    React,
    React Native,
    JavaScript,
    Node.js,
    TypeScript,
  ]
author: <PERSON>
image: https://otechq.vercel.app/images/og-image-about.png
---

## <PERSON>

#### Software Engineer

---

**Creative and passionate Software Engineer** with over 4 years of experience in diverse tech stacks and programming languages. Known for a solid foundation in software architecture and problem-solving, with a commitment to building efficient, scalable, and user-focused applications.

## Skills Summary

---

#### Technical Skills

- **Frontend Development**: React, Next.js, React Native, Vanilla JavaScript, HTML, CSS, Tailwind CSS
- **Backend Development**: Node.js, Express.js, Firebase, RESTful APIs
- **Database Management**: PostgreSQL, MySQL, ClickHouse, dbt
- **DevOps & Deployment**: Docker, Dokku, Vercel, Google Cloud Platform (GCP)
- **Data Visualization**: Apache Superset, k6 Load Testing, Unlighthouse
- **Mobile Development**: React Native, Android Studio, Xcode, Deep Linking
- **Testing & Optimization**: Performance monitoring, bug resolution, load testing
- **Tools & Platforms**: Google Analytics 4 (GA-4), Google Tag Manager (GTM), Redux, Redux-Saga

#### Soft Skills

- Team leadership and mentoring
- Curriculum design and program management
- Cross-functional collaboration
- Technical documentation and process improvement
- Agile development and project management

#### Key Strengths

- Building scalable, maintainable, and user-focused applications
- Optimizing performance and ensuring high availability for web and mobile platforms
- Designing and implementing analytics dashboards and data-driven solutions
- Migrating and modernizing legacy systems for long-term stability
- Delivering high-quality code through version control, code reviews, and best practices

## Educations

---

- 2012 - 2016 | Bachelor of Science - Institute Technology Bandung
- 2019 - 2020 | Full stack Web & Mobile Development - Purwadhika Digital Technology School

## Professional Experiences

---

#### 2024 | PT Kepoo Solusi Indonesia | Web Application Developer

_Developed an e-commerce web app for game top-ups using React and Next.js_

---

- **Architected** scalable, collaborative-friendly software, enabling efficient development and future maintainability.
- **Built and deployed** a robust e-commerce platform on **Google Cloud Platform** using **Docker** and **Google Container Engine**, supporting millions of transactions daily.
- **Implemented a design system** to ensure consistency across the app and optimized performance to handle high-traffic loads with seamless, real-time payment and status updates.
- **Enhanced SEO** using Next.js server-side rendering and best practices, improving search engine rankings and discoverability.
- **Integrated Google Tag Manager (GTM)** to track key user interactions and generate insights for business analysis.
- **Documented technical processes** thoroughly to ensure a smooth handover and ease of future development and updates.

**Technology**: React, Next.js, Typescript, Vercel, GCP, Docker, Tailwind, GA-4, GTM, k6 Load Test, Unlighthouse, etc.

#### 2022 - 2024 | FitHappy | Mobile Engineer

_Developed a wellness and health app for iOS and Android using React Native_

---

- **Enhanced software architecture** for maintainability and scalability and migrated deprecated libraries to ensure long-term support and stability.
- **Developed new features**, including user behaviour tracking, nutrition recommendations from certified nutritionists, personalized exercise routines, etc.
- **Managed app releases** and deployed updates for the App Store and Google Play, ensuring compliance with platform standards and smooth user experience.
- **Monitored performance and resolved bugs**, optimizing app functionality and reducing crash rates across both platforms.
- **Improved logging and tracking systems** to enhance performance monitoring and gather insights on user interactions.
- **Maintained code repository**, overseeing version control, pull requests, and code reviews to ensure high-quality code and streamline collaboration.
- **Documented technical processes** to support future development and foster cross-team collaboration.

**Technology**: React, React Native, JavaScript, Redux, Redux-Saga, Firebase, Android Studio, Xcode, GA-4, Deep-link, etc.

#### 2020 - 2022 | Purwadhika | Lecturer and Academic Manager

_Full-stack Web Development Program_

---

- **Led and managed a team** of 31 mentors and 21 lecturers (local and international), ensuring program delivery excellence and maintaining schedule adherence for multiple program batches.
- **Collaborated with HR** to recruit skilled mentors and lecturers, strengthening the team with industry-relevant expertise.
- **Designed and conducted programs/events** to enhance lecturer skills, fostering professional growth and knowledge sharing.
- **Organized weekly meetings** to monitor batch progress, address challenges, and ensure alignment with program goals.
- **Worked closely with the R&D team** to keep the curriculum aligned with the latest industry technologies and trends, such as the MERN stack.
- **Taught full-stack web development** as a lecturer, covering the MERN stack (MongoDB, Express, React, Node.js) and ensuring students gained the technical skills required for success.
- **Facilitated graduate success**, contributing to students securing roles in reputable companies upon program completion.

## Projects

---

#### 2024 | Dashboard Analytic System

_Developed an Analytic Dashboard for a Secure Private Messaging Platform_

---

- **Objective**: Designed and implemented an analytics dashboard for a secure private messaging platform to provide users with real-time insights and advanced data visualizations.
- **Frontend Development**: Built the user interface using Vanilla JavaScript and CSS, ensuring responsiveness and a seamless user experience.
- **Data Visualization**: Integrated Apache Superset to create dynamic, interactive charts and dashboards.
- **Database Management**: Migrated and optimized analytics data from PostgreSQL to ClickHouse using dbt, improving query performance and enabling complex analytics.
- **Deployment**: Deployed the platform on a private server using Dokku and Docker, ensuring scalability and reliability.

**Technology**: HTML + CSS, JavaScript, Apache Superset, Dokku, Docker, ClickHouse, PostgreSQL, etc.

#### 2021 | Dashboard Management System - Attendance Monitoring

_Developed a dashboard management system for the attendance monitoring system at the local bank (Bank Jambi)_

---

- **Objective**: Developed a dashboard management system for tracking employee attendance across multiple branches.
- Implemented **real-time attendance logs** that were filtered by employee, division, or branch.
- Built a customizable **report generation system** supporting multiple formats and an Excel template for payroll processing.
- Designed features for **managing attendance schedules**, including holidays, overtime, and absences.
- Developed **role-based access control (RBAC)** for managing employee and branch data securely.
- Deployed the system on a **private server**, ensuring stability and security.

**Technology**: React, Node.js, Express.js, MySQL, phpMyAdmin, Excel.js, etc.

#### 2020 | Full-Stack Developer (Graduate Program) | Parking Application

_Developed a Mobile and Web Parking System Application_

---

- **Objective**: Designed and developed a mobile application using **React Native**, enabling QR code-based check-in and check-out functionality for parking systems.
- Implemented core features such as parking status **notifications**, parking **history tracking**, multiple parking location search with **Google Maps API**, and user account and payment management.
- Built an **admin dashboard** for managing parking locations, monitoring parking data, viewing payment history, and managing user accounts.
- Developed the backend **API using Express.js**, ensuring seamless integration between the mobile app, admin dashboard, and the database.
- **Integrated payment calculations** based on parking time, offering accurate and real-time fee computation for users.
- **Optimized user experience** by implementing scheduled notifications, map-based parking searches, and real-time parking status updates.
- Gained hands-on experience in **full-stack development**, covering mobile and web app design, backend API development, and database integration.

**Technology**: React, React-Native, Node.js, Express.js, MySQL, Camera & Location SDK, Google Maps API, GCP, etc.
