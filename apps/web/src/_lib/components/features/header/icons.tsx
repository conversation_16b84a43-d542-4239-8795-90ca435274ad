import {
  Home,
  BookOpen,
  BriefcaseBusiness,
  MonitorPlay,
  UserRound,
  type LucideIcon,
} from "lucide-react";

// icon maping
export const IconMap = new Map<string, LucideIcon>([
  ["Home", Home],
  ["BookOpen", BookOpen],
  ["BriefcaseBusiness", BriefcaseBusiness],
  ["MonitorPlay", MonitorPlay],
  ["UserRound", UserRound],
]);

// define icon component
type IconProps = {
  name: string;
  size?: number;
  className?: string;
};

export function Icon({
  name,
  size = 16,
  className,
}: IconProps): React.ReactElement | undefined {
  const IconComponent = IconMap.get(name);
  if (IconComponent === undefined) return undefined;
  return <IconComponent className={className} size={size} />;
}
