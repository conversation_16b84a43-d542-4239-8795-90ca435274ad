import { twMerge } from "tailwind-merge";
import { Button } from "@web/_lib/components/ui";
import { type NavigationLinksType } from "@web/_lib/models/links";
import { Icon } from "./icons";

// local component
export function List({
  link,
  className,
  style,
}: {
  link: NavigationLinksType;
  className?: string;
  style?: React.CSSProperties;
}): React.ReactElement {
  return (
    <Button
      key={link.id}
      href={link.url ?? "#"}
      variant="outline"
      size="sm"
      className={twMerge("border-none", className)}
      style={style}
    >
      <Icon name={link.icon} className="size-4" />
      {link.name}
    </Button>
  );
}
