"use client";
import React, { useMemo, useCallback } from "react";
import Link from "next/link";
import { NavigationLinks } from "@web/_lib/models/links";
import { Icon } from "./icons";

export function AnimatedList({
  open,
  onClick,
}: {
  open: boolean;
  onClick: () => void;
}): React.ReactElement {
  // Memoize navigation data to avoid unnecessary re-renders
  const data = useMemo(
    () => (Array.isArray(NavigationLinks) ? NavigationLinks : []),
    [],
  );

  // get animation styles
  const getStyles = useCallback((isOpen: boolean): React.CSSProperties => {
    return {
      opacity: isOpen ? 1 : 0,
      transform: isOpen ? "translateY(0px)" : "translateY(20px)",
    };
  }, []);

  return (
    <div
      id="mobile-menu-list"
      className="bg-red-70 absolute top-0 left-0 flex size-full min-h-screen flex-col items-center gap-4 pt-35 md:hidden"
      style={{
        zIndex: open ? 10 : -10,
        visibility: open ? "visible" : "hidden",
      }}
    >
      {data.map((link, index) => {
        if (!link.show) return undefined;
        return (
          <Link
            key={link.id}
            href={link.url ?? "#"}
            onClick={onClick}
            className="text-dark-text border-light-gray-300 flex w-[60%] cursor-pointer items-center gap-4 rounded-md border px-4 py-2 text-center text-xl capitalize"
            style={{
              transition: "all 0.3s ease-out",
              transitionDelay: `${index * 70}ms`,
              ...getStyles(open),
            }}
          >
            <Icon name={link.icon} className="size-5" />
            {link.name}
          </Link>
        );
      })}
      <p className="text-dark-text absolute bottom-10">2025 - オタクテック</p>
    </div>
  );
}
