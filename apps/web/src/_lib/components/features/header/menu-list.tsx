import {
  type NavigationLinksType,
  NavigationLinks,
} from "@web/_lib/models/links";
import { List } from "./list";

// main component
export function MenuList(): React.ReactElement {
  return (
    <span className="hidden md:flex md:items-center md:gap-4">
      {NavigationLinks.map((link: NavigationLinksType) => {
        if (!link.show) return undefined;
        return <List key={link.id} link={link} />;
      })}
    </span>
  );
}
