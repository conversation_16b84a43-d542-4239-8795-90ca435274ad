"use client";
import React, { useState, useEffect, useCallback } from "react";
import { HumbergerMenu } from "@web/_lib/components/ui";
import { AnimatedList } from "./animated-list";

export function MobileMenu(): React.ReactElement {
  const [open, setOpen] = useState(false);

  // memoized style animation for the expanding circle
  const getStyles = useCallback((isOpen: boolean): React.CSSProperties => {
    return {
      top: isOpen ? "-32.5rem" : "2.7rem",
      right: isOpen ? "-32.5rem" : "2rem",
      width: isOpen ? "100rem" : "0rem",
      height: isOpen ? "100rem" : "0rem",
    };
  }, []);

  // manage scroll lock when the menu is open
  useEffect(() => {
    // Llck scroll when menu is open
    if (open) {
      document.body.style.overflow = "hidden";
      document.body.style.position = "fixed";
      document.body.style.width = "100%";
    } else {
      document.body.style.overflow = "auto";
      document.body.style.position = "";
      document.body.style.width = "";
    }

    return () => {
      document.body.style.overflow = "auto";
      document.body.style.position = "";
      document.body.style.width = "";
    };
  }, [open]);

  // toggle menu state
  const toggleMenu = (): void => {
    setOpen((prev) => !prev);
  };

  return (
    <React.Fragment>
      <div
        id="expanded-circle"
        className="bg-dark-background absolute z-10 flex flex-col rounded-full md:hidden"
        style={{
          transition: "all 0.25s ease-out",
          ...getStyles(open),
        }}
      />
      <AnimatedList open={open} onClick={toggleMenu} />
      <HumbergerMenu
        open={open}
        onClick={toggleMenu}
        containerClassName="z-30 block md:hidden"
        className={open ? "bg-light-card" : "bg-dark-card dark:bg-light-card"}
      />
    </React.Fragment>
  );
}
