import { services, type ServicesType } from "@web/_lib/models/services";
import ServiceCard from "./card";

export default function ServiceCards(): React.ReactElement {
  return (
    <div
      id="services-card"
      className="hidden grid-cols-1 gap-4 md:grid md:grid-cols-4"
    >
      {services.map((service: ServicesType) => (
        <ServiceCard
          key={service.id}
          no={service.no}
          title={service.title}
          description={service.description}
          background={service.background}
          icon={service.icon}
        />
      ))}
    </div>
  );
}
