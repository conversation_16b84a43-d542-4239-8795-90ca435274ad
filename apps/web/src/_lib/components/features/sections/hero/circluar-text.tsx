export default function CircularText({
  text,
  diameterInPx,
  fontSizeInPx = 12,
  textColor = "currentColor",
}: {
  text: string;
  diameterInPx: number;
  fontSizeInPx?: number;
  textColor?: string;
}): React.ReactElement {
  const pathId = "circularText";
  const radiusPx = diameterInPx / 2;
  const centerXPx = radiusPx;
  const centerYPx = radiusPx;

  const pathD = `
        M ${centerXPx},${centerYPx - radiusPx}
        A ${radiusPx},${radiusPx} 0 1 1 ${centerXPx},${centerYPx + radiusPx}
        A ${radiusPx},${radiusPx} 0 1 1 ${centerXPx},${centerYPx - radiusPx}
      `;

  return (
    <svg
      width={diameterInPx}
      height={diameterInPx}
      viewBox={`-${0.1 * diameterInPx} -${0.1 * diameterInPx} ${diameterInPx * 1.2} ${diameterInPx * 1.2}`}
      className="animate-rotate hover:animate-rotate-reverse cursor-pointer"
    >
      <path id={pathId} d={pathD} fill="none" stroke="none" />
      <text
        fill={textColor}
        style={{ fontSize: fontSizeInPx, fontWeight: "bold" }}
      >
        <textPath href={`#${pathId}`} startOffset="0%">
          {text}
        </textPath>
      </text>
    </svg>
  );
}
