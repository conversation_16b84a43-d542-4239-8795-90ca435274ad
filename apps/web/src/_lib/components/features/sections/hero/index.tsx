import { SparklesIcon, SquareArrowOutUpRightIcon } from "lucide-react";
import { LinkButton } from "@web/_lib/components/ui/button.backup";
import { WhatsappURL } from "@web/_lib/constants";
import HeroCard from "./card";

export default function HeroSection(): React.ReactElement {
  return (
    <section
      id="hero"
      className="text-light-text dark:text-dark-text flex flex-col gap-12 px-4 md:flex-row md:gap-4"
    >
      <div
        id="hero-content"
        className="flex flex-1 flex-col justify-between gap-6 md:gap-0"
      >
        <h1 className="text-4xl font-semibold text-pretty capitalize md:text-7xl">
          Let&apos;s Turn Your
          <span className="text-light-peach-700 relative cursor-pointer">
            &nbsp;Visions&nbsp;
            <SparklesIcon className="text-light-peach-700 absolute top-2 -right-2.5 size-3.5 md:size-5.5" />
          </span>
          &nbsp;&nbsp;into Smart Tech That Works for You
        </h1>
        <p className="text-base text-balance md:text-pretty">
          Hi, I&apos;m <PERSON> — I build websites that rank, apps that perform, and
          platforms that scale. Whether you&apos;re starting with a big idea or
          a small pain point, I&apos;ll help turn your idea into digital
          solutions that work for you.
        </p>
        <div id="hero-cta" className="flex gap-4">
          <LinkButton
            href={WhatsappURL}
            target="#_blank"
            rel="noopener noreferrer"
            className="bg-dark-card dark:bg-light-card dark:text-light-text text-dark-text"
          >
            Let&apos;s Talk!
          </LinkButton>
          <LinkButton
            className="hover:bg-dark-card dark:hover:bg-light-card hover:dark:text-light-text hover:text-dark-text flex items-center gap-2"
            href="/about"
          >
            About Me
            <SquareArrowOutUpRightIcon className="size-4" />
          </LinkButton>
        </div>
      </div>
      <HeroCard />
    </section>
  );
}
