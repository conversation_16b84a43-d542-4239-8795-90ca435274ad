import React from "react";
import { MailIcon, PhoneIcon } from "lucide-react";
import {
  MediaSocialLinks,
  NavigationLinks,
  type MediaSocialLinksType,
  type NavigationLinksType,
} from "@web/_lib/models/links";
import { LinkButton } from "@web/_lib/components/ui/button.backup";
import { FooterLink, FooterSocialMediaLink } from "./footer-link";
import Copyright from "./copryright";

export default function Footer(): React.ReactElement {
  return (
    <React.Fragment>
      <footer className="bg-dark-background dark:bg-dark-card text-dark-text flex w-full justify-center py-8">
        <div className="grid w-full max-w-7xl grid-cols-[8rem_1fr] gap-4 p-4 md:grid-cols-4">
          <span>
            <LinkButton
              className="p-0 text-2xl font-black md:row-auto"
              href="/"
            >
              OTECHQ.
            </LinkButton>
          </span>
          <ul className="flex flex-col gap-2 text-sm">
            <li className="text-lg font-semibold">Let&apos;s Connect</li>
            <li className="flex cursor-pointer items-center gap-2">
              <MailIcon className="size-4" />
              <p><EMAIL></p>
            </li>
            <li className="flex cursor-pointer items-center gap-2">
              <PhoneIcon className="size-4" />
              <p>+6285946646906</p>
            </li>
          </ul>
          <ul className="flex flex-col gap-2 text-sm">
            <li className="text-lg font-semibold">Links</li>
            {NavigationLinks.map(
              (link: NavigationLinksType): React.ReactElement | undefined => {
                if (!link.show) return undefined;
                return (
                  <FooterLink key={link.id} url={link.url} name={link.name} />
                );
              },
            )}
          </ul>
          <ul className="col-start-1 row-start-2 flex flex-col gap-2 text-sm md:col-start-4 md:row-start-1">
            <li className="text-lg font-semibold">Follow Me</li>
            <ul className="flex gap-4">
              {MediaSocialLinks.map(
                (link: MediaSocialLinksType): React.ReactElement => (
                  <FooterSocialMediaLink
                    key={link.id}
                    alt={link.name}
                    icon={link.lightIcon}
                    link={link.url}
                  />
                ),
              )}
            </ul>
          </ul>
        </div>
      </footer>
      <Copyright />
    </React.Fragment>
  );
}
