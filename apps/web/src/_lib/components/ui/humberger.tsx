import clsx from "clsx";
import { twMerge } from "tailwind-merge";

type HumbergerMenuProps = {
  open?: boolean;
  className?: string;
  containerClassName?: string;
  onClick?: () => void;
};

function Span({ className }: { className?: string }): React.ReactElement {
  return (
    <span
      className={twMerge(
        "absolute left-0 h-1 w-full rounded-full bg-gray-900 transition-transform duration-200 ease-in-out",
        className,
      )}
    ></span>
  );
}

export function HumbergerMenu({
  open = false,
  className,
  containerClassName,
  onClick,
}: HumbergerMenuProps): React.ReactElement {
  return (
    <div
      id="humberger"
      className={twMerge("relative h-6 w-7 cursor-pointer", containerClassName)}
      onClick={onClick}
    >
      <Span
        className={clsx(open ? "w-0 translate-y-2.5" : "top-0", className)}
      />
      <Span
        className={clsx("top-2.5", open ? "rotate-45" : "rotate-0", className)}
      />
      <Span
        className={clsx("top-2.5", open ? "-rotate-45" : "rotate-0", className)}
      />
      <Span
        className={clsx(open ? "w-0 -translate-y-2.5" : "bottom-0", className)}
      />
    </div>
  );
}
