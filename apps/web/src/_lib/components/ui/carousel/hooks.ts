"use client";
import { useState } from "react";

type useCarouselReturnType = {
  currentIndex: number;
  onNext: () => void;
  onPrev: () => void;
};

export default function useCarousel(
  length: number, // Length data of the carousel
): useCarouselReturnType {
  const [currentIndex, setCurrentIndex] = useState(0);

  // event handlers
  const onNext = (): void => {
    setCurrentIndex((prev) => (prev + 1) % length);
  };
  const onPrev = (): void => {
    setCurrentIndex((prev) => (prev - 1 + length) % length);
  };

  return {
    currentIndex,
    onNext,
    onPrev,
  };
}

export function useStyles(
  index: number,
  currentIndex: number,
  length: number,
  depth: number,
  spacing: number,
): [React.CSSProperties, number | string] {
  const offset = (index - currentIndex + length) % length;
  let zIndex = 0; // z-index for the current card
  let dx = 0; // x-axis translation
  let dz = 0; // z-axis translation
  let opacity = 1; // opacity

  // only translate when if offset is 0, 1, 2
  switch (offset) {
    case 0: // Current card
      zIndex = length; // z-index for the current card
      break;
    case 1: // 1st next card
      dx = spacing;
      dz = -depth;
      zIndex = length - 1; // z-index for the next card
      break;
    case 2: // 2nd next card
      dx = 2 * spacing;
      dz = -2 * depth;
      zIndex = length - 2; // z-index for the next card
      break;
    case length - 1: // @outgoing card
      dx = -1.5 * spacing;
      dz = -depth;
      opacity = 0;
      break;
    default: // @other cards
      dx = 2 * offset * spacing;
      dz = -2 * depth;
      opacity = 0;
      break;
  }

  const styles: React.CSSProperties = {
    transform: `translateX(${dx}px) translateZ(${dz}px) rotateY(0deg)`,
    opacity,
  };
  return [styles, zIndex];
}
