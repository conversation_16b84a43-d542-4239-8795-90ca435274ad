import { twMerge } from "tailwind-merge";
import useCarousel from "./hooks";
import CarouselControl from "./control";

type CarouselProps = {
  height: number | string;
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
};

export default function CarouselContainer({
  height,
  children,
  className,
  style,
  ...props
}: CarouselProps): React.ReactElement {
  return (
    <div
      id="container-carousel"
      className={twMerge("relative flex", className)}
      style={{
        ...style,
        height,
        width: "100%",
        margin: "0 auto",
        perspective: "1000px",
        transformStyle: "preserve-3d",
      }}
      {...props}
    >
      {children}
    </div>
  );
}

export { useCarousel, CarouselContainer, CarouselControl };
