import { twMerge } from "tailwind-merge";
import { ChevronRightIcon, ChevronLeftIcon } from "lucide-react";

function Button({
  onClick,
  children,
}: {
  onClick: () => void;
  children: React.ReactNode;
}): React.ReactElement {
  return (
    <button
      className="bg-light-background flex size-10 cursor-pointer items-center justify-center rounded-full transition-all duration-300 ease-in active:scale-70"
      onClick={onClick}
    >
      {children}
    </button>
  );
}

export default function CarouselControl({
  onNext,
  onPrev,
  className,
}: {
  onNext: () => void;
  onPrev: () => void;
  className?: string;
}): React.ReactElement {
  return (
    <div id="control" className={twMerge("flex items-center gap-2", className)}>
      <Button onClick={onPrev}>
        <ChevronLeftIcon className="text-light-text size-5" />
      </Button>
      <Button onClick={onNext}>
        <ChevronRightIcon className="text-light-text size-5" />
      </Button>
    </div>
  );
}
