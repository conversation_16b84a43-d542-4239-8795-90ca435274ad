import { twMerge } from "tailwind-merge";
import Link from "next/link";

// Types
type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  children: React.ReactNode;
  className?: string;
};
type LinkButtonProps = React.AnchorHTMLAttributes<HTMLAnchorElement> & {
  href: string;
  children: React.ReactNode;
  className?: string;
};

// Main Styles
const MainStyles =
  "hover:shadow-deep cursor-pointer select-none rounded-sm px-4 py-2 transition-all duration-200 ease-in-out hover:-translate-y-0.5 hover:scale-[1.03] active:scale-90";

export default function Button({
  children,
  className,
  ...props
}: ButtonProps): React.ReactElement {
  return (
    <button className={twMerge(MainStyles, className)} {...props}>
      {children}
    </button>
  );
}

function TextButton({
  children,
  className,
  ...props
}: ButtonProps): React.ReactElement {
  return (
    <button
      className={twMerge(
        MainStyles,
        "text-light-text hover:font-semibold",
        className,
      )}
      {...props}
    >
      {children}
    </button>
  );
}

function LinkButton({
  href,
  children,
  className,
  ...props
}: LinkButtonProps): React.ReactElement {
  return (
    <Link href={href} className={twMerge(MainStyles, className)} {...props}>
      {children}
    </Link>
  );
}

export { Button, TextButton, LinkButton };
