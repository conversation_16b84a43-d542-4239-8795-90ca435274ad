@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";
@plugin "@tailwindcss/typography";
@variant dark (&:where([data-theme="dark"], [data-theme="dark"] *));
@config "../../tailwind.config.js";

@theme {
  /* Font style */
  --font-montserrat: "Montserrat", sans-serif;

  /* Animation style */
  --animate-rotate: rotate 20s linear infinite;
  --animate-rotate-reverse: rotate 20s linear infinite reverse;
  --animate-pause: animation-play-state: paused !important;
  @keyframes rotate {
    to {
      transform: rotate(360deg);
    }
  }

  /* Original base background/text colors */
  --color-background: #e9e4f5;
  --color-text: #3d3d3d;
  --color-shadow: rgba(0, 0, 0, 0.1);
  --color-deep-shadow: rgba(0, 0, 0, 0.15);

  /* Main Theme Colors (for backgrounds, text, cards, borders) */
  /* Light Variants */
  --color-light-background: #f8f8f8;
  --color-light-text: #333333;
  --color-light-card: #ffffff;
  --color-light-border: #e0e0e0;

  /* Dark Variants */
  --color-dark-background: #2d3436;
  --color-dark-text: #ecf0f1;
  --color-dark-card: #3c4245;
  --color-dark-border: #4a5153;

  /* Extended Colors - Light Variants */
  /* Purple */
  --color-light-purple-50: #f0edff;
  --color-light-purple-100: #e0daff;
  --color-light-purple-200: #c2b6ff;
  --color-light-purple-300: #a29bfe; /* Original light purple */
  --color-light-purple-400: #887cef;
  --color-light-purple-500: #7162df;
  --color-light-purple-600: #5c4dd0;
  --color-light-purple-700: #4b3caf;
  --color-light-purple-800: #3a2f9a;
  --color-light-purple-900: #29208a;
  --color-light-purple-950: #1c1560;

  /* Yellow */
  --color-light-yellow-50: #fffcf0;
  --color-light-yellow-100: #fff9e0;
  --color-light-yellow-200: #fff4c2;
  --color-light-yellow-300: #ffeaa7; /* Original light yellow */
  --color-light-yellow-400: #ffdf89;
  --color-light-yellow-500: #fdd16b;
  --color-light-yellow-600: #eab94d;
  --color-light-yellow-700: #d89f3a;
  --color-light-yellow-800: #c58627;
  --color-light-yellow-900: #b26d13;
  --color-light-yellow-950: #804a00;

  /* Orange */
  --color-light-orange-50: #fffafa;
  --color-light-orange-100: #fff5f2;
  --color-light-orange-200: #fee7e0;
  --color-light-orange-300: #fab1a0; /* Original light orange */
  --color-light-orange-400: #e09c8f;
  --color-light-orange-500: #c5877e;
  --color-light-orange-600: #aa736d;
  --color-light-orange-700: #90605d;
  --color-light-orange-800: #764d4d;
  --color-light-orange-900: #5c3a3c;
  --color-light-orange-950: #3d2628;

  /* Peach */
  --color-light-peach-50: #fffbfb;
  --color-light-peach-100: #fff5f7;
  --color-light-peach-200: #ffe8ec;
  --color-light-peach-300: #ffc0cb; /* Original light peach */
  --color-light-peach-400: #f9a7b3;
  --color-light-peach-500: #f08e9b;
  --color-light-peach-600: #e67584;
  --color-light-peach-700: #dc5c6e;
  --color-light-peach-800: #d34357;
  --color-light-peach-900: #c92a41;
  --color-light-peach-950: #b01a2d;

  /* Pink */
  --color-light-pink-50: #fdf9fb;
  --color-light-pink-100: #faedf3;
  --color-light-pink-200: #f5dde7;
  --color-light-pink-300: #fbbde3; /* Original light pink */
  --color-light-pink-400: #eca3d6;
  --color-light-pink-500: #db8ac9;
  --color-light-pink-600: #c972bb;
  --color-light-pink-700: #b759ae;
  --color-light-pink-800: #a540a0;
  --color-light-pink-900: #932793;
  --color-light-pink-950: #7a1c7a;

  /* Red */
  --color-light-red-50: #fff8f8;
  --color-light-red-100: #ffeeec;
  --color-light-red-200: #ffe0de;
  --color-light-red-300: #ffabab; /* Original light red */
  --color-light-red-400: #e69696;
  --color-light-red-500: #cd8181;
  --color-light-red-600: #b46c6c;
  --color-light-red-700: #9b5757;
  --color-light-red-800: #824242;
  --color-light-red-900: #692d2d;
  --color-light-red-950: #4b1a1a;

  /* Green */
  --color-light-green-50: #f8fff8;
  --color-light-green-100: #f0fef0;
  --color-light-green-200: #e0fbe0;
  --color-light-green-300: #b7e0b7; /* Original light green */
  --color-light-green-400: #a2c7a2;
  --color-light-green-500: #8db08d;
  --color-light-green-600: #789978;
  --color-light-green-700: #638263;
  --color-light-green-800: #4e6b4e;
  --color-light-green-900: #395439;
  --color-light-green-950: #263b26;

  /* Gray */
  --color-light-gray-50: #f6f7f9;
  --color-light-gray-100: #edf0f2;
  --color-light-gray-200: #e0e2e5;
  --color-light-gray-300: #d1d5db; /* Original light gray */
  --color-light-gray-400: #bcc0c7;
  --color-light-gray-500: #a7abae;
  --color-light-gray-600: #92969a;
  --color-light-gray-700: #7d8186;
  --color-light-gray-800: #686c72;
  --color-light-gray-900: #53575e;
  --color-light-gray-950: #3a3e47;

  /* Extended Colors - Dark Variants */
  /* Purple */
  --color-dark-purple-50: #1c1560;
  --color-dark-purple-100: #29208a;
  --color-dark-purple-200: #3a2f9a;
  --color-dark-purple-300: #4b3caf;
  --color-dark-purple-400: #5c4dd0;
  --color-dark-purple-500: #7162df;
  --color-dark-purple-600: #806ee0;
  --color-dark-purple-700: #8e7cef; /* Original dark purple */
  --color-dark-purple-800: #a29bfe;
  --color-dark-purple-900: #c2b6ff;
  --color-dark-purple-950: #e0daff;

  /* Yellow */
  --color-dark-yellow-50: #804a00;
  --color-dark-yellow-100: #b26d13;
  --color-dark-yellow-200: #c58627;
  --color-dark-yellow-300: #d89f3a;
  --color-dark-yellow-400: #eab94d;
  --color-dark-yellow-500: #fdd16b;
  --color-dark-yellow-600: #fde080;
  --color-dark-yellow-700: #ffda89; /* Original dark yellow */
  --color-dark-yellow-800: #fff4c2;
  --color-dark-yellow-900: #fff9e0;
  --color-dark-yellow-950: #fffcf0;

  /* Orange */
  --color-dark-orange-50: #3d2628;
  --color-dark-orange-100: #5c3a3c;
  --color-dark-orange-200: #764d4d;
  --color-dark-orange-300: #90605d;
  --color-dark-orange-400: #aa736d;
  --color-dark-orange-500: #c5877e;
  --color-dark-orange-600: #d5988f;
  --color-dark-orange-700: #e08a7c; /* Original dark orange */
  --color-dark-orange-800: #fab1a0;
  --color-dark-orange-900: #fee7e0;
  --color-dark-orange-950: #fff5f2;

  /* Peach */
  --color-dark-peach-50: #b01a2d;
  --color-dark-peach-100: #c92a41;
  --color-dark-peach-200: #d34357;
  --color-dark-peach-300: #dc5c6e;
  --color-dark-peach-400: #e67584;
  --color-dark-peach-500: #f08e9b;
  --color-dark-peach-600: #f5a7b3;
  --color-dark-peach-700: #ff99a4; /* Original dark peach */
  --color-dark-peach-800: #ffc0cb;
  --color-dark-peach-900: #ffe8ec;
  --color-dark-peach-950: #fff5f7;

  /* Pink */
  --color-dark-pink-50: #7a1c7a;
  --color-dark-pink-100: #932793;
  --color-dark-pink-200: #a540a0;
  --color-dark-pink-300: #b759ae;
  --color-dark-pink-400: #c972bb;
  --color-dark-pink-500: #db8ac9;
  --color-dark-pink-600: #e09ad1;
  --color-dark-pink-700: #e091c6; /* Original dark pink */
  --color-dark-pink-800: #fbbde3;
  --color-dark-pink-900: #f5dde7;
  --color-dark-pink-950: #faedf3;

  /* Red */
  --color-dark-red-50: #4b1a1a;
  --color-dark-red-100: #692d2d;
  --color-dark-red-200: #824242;
  --color-dark-red-300: #9b5757;
  --color-dark-red-400: #b46c6c;
  --color-dark-red-500: #cd8181;
  --color-dark-red-600: #dc9191;
  --color-dark-red-700: #e07d7d; /* Original dark red */
  --color-dark-red-800: #ffabab;
  --color-dark-red-900: #ffe0de;
  --color-dark-red-950: #ffeeec;

  /* Green */
  --color-dark-green-50: #263b26;
  --color-dark-green-100: #395439;
  --color-dark-green-200: #4e6b4e;
  --color-dark-green-300: #638263;
  --color-dark-green-400: #789978;
  --color-dark-green-500: #8db08d;
  --color-dark-green-600: #9ac09a;
  --color-dark-green-700: #8cc08c; /* Original dark green */
  --color-dark-green-800: #b7e0b7;
  --color-dark-green-900: #e0fbe0;
  --color-dark-green-950: #f0fef0;

  /* Gray */
  --color-dark-gray-50: #3a3e47;
  --color-dark-gray-100: #53575e;
  --color-dark-gray-200: #686c72;
  --color-dark-gray-300: #7d8186;
  --color-dark-gray-400: #92969a;
  --color-dark-gray-500: #a7abae;
  --color-dark-gray-600: #bac0c7;
  --color-dark-gray-700: #6b7280; /* Original dark gray */
  --color-dark-gray-800: #d1d5db;
  --color-dark-gray-900: #e0e2e5;
  --color-dark-gray-950: #edf0f2;

  /* Shadow */
  --shadow-normal: 0 10px 25px -5px var(--color-shadow);
  --shadow-deep: 0 20px 30px -10px var(--color-deep-shadow);
}
