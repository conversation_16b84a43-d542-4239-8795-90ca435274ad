import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  // This must available at run time
  server: {
    NODE_ENV: z.enum(["development", "test", "production"]),
    GTM_ID: z.string().min(1),
    BASE_URL: z.string().min(1),
    REMOTE_IMAGE_BASE_URL: z.string().min(1),
  },
  // This must available at build time
  client: {
    NEXT_PUBLIC_BASE_URL: z.string().min(1),
    NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL: z.string().min(1),
  },
  experimental__runtimeEnv: {
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL,
    NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL:
      process.env.NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL,
  },
  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
  emptyStringAsUndefined: true,
});
