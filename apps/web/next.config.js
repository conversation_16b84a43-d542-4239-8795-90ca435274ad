/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import createMDX from "@next/mdx";
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  // For Docker
  output: "standalone",
  pageExtensions: ["jsx", "js", "ts", "tsx", "mdx"],
  // Add this to ensure React 19 compatibility
  experimental: {
    optimizePackageImports: ["react", "react-dom"],
  },
  // Remote image
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "res.cloudinary.com",
        port: "",
        pathname: "/**",
      },
    ],
  },
  // Loging
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  // Remove console.log in production
  compiler: {
    removeConsole: {
      exclude: ["error", "warn"],
    },
  },
};

// mdx support
const withMDX = createMDX({
  extension: /\.mdx?$/,
});
export default withMDX(config);
