{"name": "@otechq/web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.2", "@next/third-parties": "^15.3.3", "@t3-oss/env-nextjs": "^0.12.0", "@types/mdx": "^2.0.13", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "lucide-react": "^0.514.0", "next": "^15.2.3", "next-mdx-remote": "^5.0.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-scroll": "^1.9.3", "rehype-slug": "^6.0.0", "tailwind-merge": "^3.2.0", "tailwind-variants": "^2.1.0", "uuid": "^11.1.0", "zod": "^3.24.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --ext .ts,.tsx,.js,.jsx"], "*.{js,jsx,ts,tsx,json,cjs,mjs,md}": ["prettier --write"]}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@next/eslint-plugin-next": "^15.3.4", "@tailwindcss/postcss": "^4.0.15", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.14.10", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-scroll": "^1.8.10", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "eslint-plugin-react-hooks": "^5.2.0", "lint-staged": "^15.5.2", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.15", "typescript": "^5.8.2", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.3.0"}