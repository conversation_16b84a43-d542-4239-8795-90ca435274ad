# --- Stage 1: Base image for all builds ---
# Use the official Node.js 20 Alpine image as the base image
FROM node:20-alpine AS base

# Install pnpm using corepack in Docker
# This ensures corepack is enabled and pnpm is activated globally for this stage.
RUN corepack enable && corepack prepare pnpm@latest --activate

# --- Stage 2: Dependencies Installation ---
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat

# Set root working directory
WORKDIR /app

# Copy the root and app package.json, pnpm-lock.yaml, and pnpm-workspace.yaml
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml* ./

# Copy the web app package.json
COPY apps/web/package.json ./apps/web/

# Install only web app + its dependencies
RUN pnpm install --frozen-lockfile

# --- Stage 3: Build the Next.js Application ---
FROM base AS builder
WORKDIR /app

# Copy the entire /app directory from deps stage (preserves pnpm workspace symlinks)
COPY --from=deps /app /app

# Copy source code
COPY apps/web ./apps/web

# Environment variables for build time
ARG NEXT_PUBLIC_BASE_URL
ARG NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL

# Set environment variables
ENV NEXT_PUBLIC_BASE_URL=$NEXT_PUBLIC_BASE_URL
ENV NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL=$NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL

# Build the application and skip environment validation
ENV NEXT_TELEMETRY_DISABLED=1
RUN SKIP_ENV_VALIDATION=1 pnpm run web:build

# --- Stage 4: Production Runner Image ---
FROM node:20-alpine AS runner
WORKDIR /app

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the built application
# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone/  ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public apps/web/public
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static apps/web/.next/static

USER nextjs

# Environment variables for runtime
ENV NODE_ENV=production
ENV PORT=3001
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1

# Runtime environment variables (these will be overridden by docker run -e or docker-compose)
ENV GTM_ID=""
ENV BASE_URL=""
ENV REMOTE_IMAGE_BASE_URL=""

EXPOSE 3001

CMD ["node", "apps/web/server.js"]