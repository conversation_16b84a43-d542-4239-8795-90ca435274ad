# Otechq Monorepo

This repository contains the Otechq web application and related services, managed as a monorepo. It is containerized for production using Docker and orchestrated with Docker Compose.

---

## Project Requirements

- [Node.js](https://nodejs.org/) (v18+ recommended)
- [pnpm](https://pnpm.io/) (for monorepo management)
- [Docker](https://www.docker.com/) & [Docker Compose](https://docs.docker.com/compose/)

---

## Project Structure

```sh
.
├── apps/
│   └── web/                # Next.js web application
│       ├── Dockerfile      # Dockerfile for web app
│       ├── src/            # Source code
│       ├── public/         # Static assets
│       └── ...
├── docker-compose.yaml     # Docker Compose for multi-app orchestration
├── package.json            # Monorepo root package
├── pnpm-workspace.yaml     # pnpm workspace config
└── ...
```

---

## Setup & Installation

1. **Clone the repository:**

   ```sh
   git clone https://github.com/alee0510/otechq.git
   cd otechq
   ```

2. **Install dependencies:**

   ```sh
   pnpm install
   ```

---

## Running the Project (Development)

### Web App

```sh
cd apps/web
pnpm web:dev
```

---

## Running in Production with Docker

### 1. Build and Run All Apps with Docker Compose

```sh
docker-compose up --build
```

- This will build and start all services defined in `docker-compose.yaml`.
- Each app (e.g., `web`) uses its own `Dockerfile` for production builds.

### 2. Build and Run a Single App (e.g., Web)

```sh
cd apps/web
docker build -t otechq-web .
docker run -p 3000:3000 otechq-web
```

---

## Running Tests in Production (Docker)

To run tests inside the Docker container for an app:

1. **Build the Docker image:**

   ```sh
   cd apps/web
   docker build -t otechq-web .
   ```

2. **Run tests inside the container:**

   ```sh
   docker run --rm otechq-web pnpm test
   ```

   - Replace `pnpm test` with the actual test command for the app.

---

## Additional Notes

- **Environment Variables:**
  - Configure environment variables as needed for each app (see `.env.example` if available).
- **Linting & Formatting:**
  - Run `pnpm lint` and `pnpm format` for code quality.
- **Adding New Apps:**
  - Add new apps under the `apps/` directory and update `docker-compose.yaml` as needed.

---

## License

MIT
