services:
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      args:
        - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}
        - NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL=${NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}
    image: otechq-web:1.0.0
    ports:
      - "3001:3000"
    env_file:
      - ./.env
    environment:
      - GTM_ID=${GTM_ID}
      - BASE_URL=${BASE_URL}
      - REMOTE_IMAGE_BASE_URL=${REMOTE_IMAGE_BASE_URL}
    container_name: otechq-web
    restart: unless-stopped
