services:
  traefik:
    image: "traefik:latest"
    env_file:
      - ./.env
    command:
      - "--log.level=DEBUG"
      # For local development only, disable in production
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      # Redirect from HTTP to HTTPS, production only
      # - "--entrypoints.websecure.address=:443"
      # - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      # - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      # TLS certificates, production only
      # - "--certificatesresolvers.myresolver.acme.httpchallenge=true"
      # - "--certificatesresolvers.myresolver.acme.email=${TRAEFIK_ACME_EMAIL}"
      # - "--certificatesresolvers.myresolver.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80" # HTTP
      # - "443:443" # HTTPS, production only
      - "8080:8080" # Uncomment this line for local development only
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      # Uncomment the following line for production only
      # - "/letsencrypt:/letsencrypt:rw"
    container_name: traefik
    networks:
      - otechq_proxy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`${TRAEFIK_DOMAIN_NAME}`)"
      # For local development only, disable in production
      - "traefik.http.routers.traefik.entrypoints=web"
      # Uncomment the following lines for production only
      # - "traefik.http.routers.traefik.entrypoints=websecure"
      # - "traefik.http.routers.traefik.tls.certresolver=myresolver"
      # Traefik dashboard configuration
      - "traefik.http.routers.traefik.service=api@internal"
      - "traefik.http.routers.traefik.middlewares=traefik-auth"
      - "traefik.http.middlewares.traefik-auth.basicauth.users=${TRAEFIK_AUTH_USER}:${TRAEFIK_AUTH_PASSWORD}"
  web:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      args:
        - NEXT_PUBLIC_BASE_URL=${NEXT_PUBLIC_BASE_URL}
        - NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL=${NEXT_PUBLIC_REMOTE_IMAGE_BASE_URL}
    image: otechq-web:1.0.0
    env_file:
      - ./.env
    environment:
      - GTM_ID=${GTM_ID}
      - BASE_URL=${BASE_URL}
      - REMOTE_IMAGE_BASE_URL=${REMOTE_IMAGE_BASE_URL}
    # Load balancing, production only
    # deploy:
    #   mode: replicated
    #   replicas: 3
    container_name: otechq-web
    networks:
      - otechq_proxy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.web.rule=Host(`${APP_DOMAIN_NAME}`)"
      # For local development only, disable in production
      - "traefik.http.routers.web.entrypoints=web"
      # Uncomment the following lines for production only
      # - "traefik.http.routers.web.entrypoints=websecure"
      # - "traefik.http.routers.web.tls.certresolver=myresolver"
      - "traefik.http.services.web.loadbalancer.server.port=3000"
# Uncomment the following line for production only
# volumes:
#   letsencrypt:
networks:
  otechq_proxy:
    external: true
